package org.example.data

import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.map
import org.example.core.domain.model.Product
import org.example.core.domain.model.Receipt
import org.example.core.domain.repository.ReceiptRepository
import org.example.core.utils.DateUtils
import org.example.data.database.AppDatabase
import org.example.data.database.entity.ProductEntity
import org.example.data.database.entity.ReceiptEntity
import org.example.data.database.service.HighlightingService

class ReceiptRepositoryImpl(
    private val database: AppDatabase
) : ReceiptRepository {

    private val receiptDao = database.receiptDao()
    private val productDao = database.productDao()
    private val highlightingService = HighlightingService(database)

    override suspend fun saveReceipt(receipt: Receipt) {
        println("saveReceipt: $receipt")
        // Insert receipt
        val receiptEntity = ReceiptEntity.fromDomainModel(receipt)
        receiptDao.insertReceipt(receiptEntity)

        // Insert products
        val productEntities = receipt.products.map { ProductEntity.fromDomainModel(it) }
        productDao.insertProducts(productEntities)

        // Save highlighting data for products that have it
        receipt.products.forEach { product ->
            println("product.ocrGroupedTextLine: ${product.ocrGroupedTextLine}")
            product.ocrGroupedTextLine?.let { groupedTextLine ->
                println("product.ocrGroupedTextLine?.let { $groupedTextLine")
                highlightingService.saveGroupedTextLine(groupedTextLine, product.id)
            }
        }
    }

    override suspend fun getReceiptById(id: String): Receipt? {
        val receiptEntity = receiptDao.getReceiptById(id) ?: return null
        return loadReceiptWithHighlighting(receiptEntity)
    }

    override fun getAllReceipts(): Flow<List<Receipt>> {
        return receiptDao.getAllReceipts().map { receiptEntities ->
            receiptEntities.map { receiptEntity ->
                loadReceiptWithHighlighting(receiptEntity)
            }
        }
    }

    override suspend fun deleteReceipt(id: String) {
        receiptDao.deleteReceiptById(id)
        // Products will be deleted automatically due to CASCADE foreign key
    }

    override suspend fun updateReceipt(receipt: Receipt) {
        // Update receipt
        val receiptEntity = ReceiptEntity.fromDomainModel(receipt)
        receiptDao.updateReceipt(receiptEntity)

        // Get existing product IDs to clean up highlighting data
        val existingProductEntities = productDao.getProductsByReceiptId(receipt.id)
        val existingProductIds = existingProductEntities.map { it.id }

        // Delete existing highlighting data
        highlightingService.deleteGroupedTextLinesByProductIds(existingProductIds)

        // Delete existing products and insert new ones
        productDao.deleteProductsByReceiptId(receipt.id)
        val productEntities = receipt.products.map { ProductEntity.fromDomainModel(it) }
        productDao.insertProducts(productEntities)

        // Save highlighting data for products that have it
        receipt.products.forEach { product ->
            product.ocrGroupedTextLine?.let { groupedTextLine ->
                highlightingService.saveGroupedTextLine(groupedTextLine, product.id)
            }
        }
    }

    override fun searchReceiptsByStoreName(storeName: String): Flow<List<Receipt>> {
        return receiptDao.searchReceiptsByStoreName(storeName).map { receiptEntities ->
            receiptEntities.map { receiptEntity ->
                loadReceiptWithHighlighting(receiptEntity)
            }
        }
    }

    override fun getReceiptsByDateRange(startDate: String, endDate: String): Flow<List<Receipt>> {
        val startTimestamp = DateUtils.isoStringToTimestamp(startDate)
        val endTimestamp = DateUtils.isoStringToTimestamp(endDate)
        return receiptDao.getReceiptsByDateRange(startTimestamp, endTimestamp)
            .map { receiptEntities ->
                receiptEntities.map { receiptEntity ->
                    loadReceiptWithHighlighting(receiptEntity)
                }
            }
    }

    override fun getReceiptsFromDate(fromDate: String): Flow<List<Receipt>> {
        val fromTimestamp = DateUtils.isoStringToTimestamp(fromDate)
        return receiptDao.getReceiptsFromDate(fromTimestamp).map { receiptEntities ->
            receiptEntities.map { receiptEntity ->
                loadReceiptWithHighlighting(receiptEntity)
            }
        }
    }

    override fun getReceiptsToDate(toDate: String): Flow<List<Receipt>> {
        val toTimestamp = DateUtils.isoStringToTimestamp(toDate)
        return receiptDao.getReceiptsToDate(toTimestamp).map { receiptEntities ->
            receiptEntities.map { receiptEntity ->
                loadReceiptWithHighlighting(receiptEntity)
            }
        }
    }

    /**
     * Helper method to load a receipt with its products and highlighting data
     */
    private suspend fun loadReceiptWithHighlighting(receiptEntity: ReceiptEntity): Receipt {
        val productEntities = productDao.getProductsByReceiptId(receiptEntity.id)

        // Load highlighting data for all products
        val productIds = productEntities.map { it.id }
        val highlightingData = highlightingService.getGroupedTextLinesByProductIds(productIds)

        // Convert products with highlighting data
        val products = productEntities.map { productEntity ->
            val groupedTextLine = highlightingData[productEntity.id]
            productEntity.toDomainModel(groupedTextLine)
        }

        return receiptEntity.toDomainModel(products)
    }
}
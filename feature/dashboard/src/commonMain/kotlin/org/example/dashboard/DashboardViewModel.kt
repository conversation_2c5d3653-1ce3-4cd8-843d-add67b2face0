package org.example.dashboard

import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import kotlinx.coroutines.ExperimentalCoroutinesApi
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.flow.distinctUntilChanged
import kotlinx.coroutines.flow.flatMapLatest
import kotlinx.coroutines.flow.map
import kotlinx.coroutines.flow.update
import kotlinx.coroutines.launch
import org.example.core.domain.model.Category
import org.example.core.domain.model.Receipt
import org.example.core.domain.usecase.categories.GetTypesAndCategoriesUseCase
import org.example.core.domain.usecase.receipt.GetReceiptsUseCase
import org.example.shared.component.DateFilter
import org.example.shared.component.DateFilterUtils

data class ReceiptData(
    val id: String,
    val purchaseDate: String,
    val receiptSum: String = "0,00",
    val categoryBreakdownCostam: String = ""
)

data class CategorySpending(
    val categoryName: String,
    val categorySum: Long
)

data class DashboardUiState(
    val receipts: List<ReceiptData> = emptyList(),
    val totalSpending: Long = 0,
    val categorySpendings: List<CategorySpending> = emptyList(),
    val currentFilter: DateFilter = DateFilter.All,
    val isFilterExpanded: Boolean = false,
    val availableFilters: List<DateFilter> = DateFilter.getDefaultFilters(),
    val availableCategories: List<Category> = emptyList()
)


class DashboardViewModel(
    private val getReceiptsUseCase: GetReceiptsUseCase,
    private val getTypesAndCategoriesUseCase: GetTypesAndCategoriesUseCase
) : ViewModel() {
    private val _uiState = MutableStateFlow(DashboardUiState())
    val uiState: StateFlow<DashboardUiState> = _uiState.asStateFlow()

    init {
        observeReceipts()
        observeCategories()
    }

    private fun observeCategories() {
        viewModelScope.launch {
            getTypesAndCategoriesUseCase.getCategories().collect { categories ->
                _uiState.update { it.copy(availableCategories = categories) }
            }
        }
    }

    @OptIn(ExperimentalCoroutinesApi::class)
    private fun observeReceipts() {
        viewModelScope.launch {
            // Observe filter changes and fetch receipts accordingly
            _uiState
                .map { it.currentFilter }
                .distinctUntilChanged()
                .flatMapLatest { filter ->
                    val dateRange = DateFilterUtils.getDateRange(filter)
                    getReceiptsUseCase.getReceiptsWithDateFilter(
                        dateRange.startDate,
                        dateRange.endDate
                    )
                }
                .collect { receipts ->
                    _uiState.update { currentState ->
                        currentState.copy(receipts = receipts.map {
                            ReceiptData(
                                id = it.id,
                                purchaseDate = it.purchaseDate,
                                receiptSum = formatPrice(it.receiptSum ?: 0)
                            )
                        },
                                categorySpendings = calculateCategorySpendings(receipts),
                            totalSpending = calculateTotalSpending(receipts)
                        )
                    }
                }
        }
    }

    private fun calculateTotalSpending(receipts: List<Receipt>): Long {
        val products = receipts.flatMap { it.products }
        return products.sumOf { it.totalInCents }
    }

    private fun calculateCategorySpendings(receipts: List<Receipt>): List<CategorySpending> {
        val products = receipts.flatMap { it.products }
        val categorySums = products.groupBy { it.category }
            .mapValues { entry ->
                entry.value.sumOf { it.totalInCents }
            }
        return categorySums.map { (category, sum) ->
            CategorySpending(category, sum)
        }
    }

    fun onFilterSelected(filter: DateFilter) {
        _uiState.update {
            it.copy(
                currentFilter = filter,
                isFilterExpanded = false
            )
        }
    }

    fun onFilterExpandedChanged(expanded: Boolean) {
        _uiState.update { it.copy(isFilterExpanded = expanded) }
    }

    fun onCustomDateSelected(startDate: String?, endDate: String?) {
        val customFilter = DateFilter.Custom(startDate, endDate)
        _uiState.update {
            it.copy(
                currentFilter = customFilter,
                isFilterExpanded = false
            )
        }
    }
}

fun formatPrice(priceInCents: Long, separator: String = ","): String {
    val baseUnit = priceInCents / 100
    val subunit = priceInCents % 100
    return "$baseUnit$separator${subunit.toString().padStart(2, '0')}"
}

